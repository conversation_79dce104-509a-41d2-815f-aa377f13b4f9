"use client";

import { useEffect } from 'react';
import Cha<PERSON><PERSON>indow from "@/components/ChatWindow";
import { toast } from 'sonner';

export default function ChatPage() {
  // Fire AdMesh conversion pixel
  useEffect(() => {
    // Get the slug from the domain
    const urlPath = window.location.pathname;
    console.log("🚀 ChatPage ~ urlPath:", urlPath);

    // Extract domain and create slug
    const hostname = window.location.hostname;
    let urlSlug;

    if (hostname === 'localhost') {
      urlSlug = 'localhost';
    } else if (hostname.includes('useadmesh.com')) {
      urlSlug = 'useadmesh';
    } else {
      // Fallback: extract main domain name
      urlSlug = hostname.split('.').slice(-2, -1)[0] || 'localhost';
    }

    console.log("🚀 ChatPage ~ urlSlug:", urlSlug);
    const target = urlSlug;

    // Try multiple possible keys
    const possibleKeys = [
      `admesh_referral_${target}`,
      `admesh_referral_default`,
      `admesh_last_referral`
    ];

    let data = null;
    let usedKey = null;

    // Check each possible key
    for (const key of possibleKeys) {
      data = localStorage.getItem(key);
      if (data) {
        usedKey = key;
        break;
      }
    }

    console.log("🚀 ChatPage ~ possibleKeys:", possibleKeys);
    console.log("🚀 ChatPage ~ usedKey:", usedKey);
    console.log("🚀 ChatPage ~ data:", data);

    // Also log all localStorage keys for debugging
    console.log("🚀 ChatPage ~ all localStorage keys:", Object.keys(localStorage));

    if (data) {
      try {
        const { clickId, source,test } = JSON.parse(data);
        if (clickId && source === 'admesh') {
          const img = document.createElement('img');
          img.src = `https://api.useadmesh.com/conversion/pixel?utm_click_id=${clickId}&&test=${test}`;
          // img.src = `http://127.0.0.1:8000/conversion/pixel?utm_click_id=${clickId}&&test=${test}`;
          img.width = 1;
          img.height = 1;
          img.style.display = 'none';
          document.body.appendChild(img);
          if (usedKey) localStorage.removeItem(usedKey); // prevent duplicate fire
          console.log('AdMesh conversion tracked:', clickId);
          toast.success('AdMesh conversion tracked!');
        }
      } catch (e) {
        console.error('AdMesh pixel error:', e);
      }
    }
  }, []);

  return <ChatWindow />;
}

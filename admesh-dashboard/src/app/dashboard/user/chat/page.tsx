"use client";

import { useEffect } from 'react';
import Chat<PERSON><PERSON>ow from "@/components/ChatWindow";
import { toast } from 'sonner';

export default function ChatPage() {
  // Fire AdMesh conversion pixel
  useEffect(() => {
    // Get the slug from the URL or pathname
    const urlPath = window.location.pathname;
    const urlSlug = urlPath.split('/').filter(Boolean).pop() || 'chat';

    // Use localhost:3000 for development, URL slug for production
    const isDev = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';
    const target = isDev ? 'localhost' : urlSlug;
    const key = `admesh_referral_${target}`;
    const data = localStorage.getItem(key);
    console.log("🚀 ~ file: page.tsx:9 ~ ChatPage ~ data:", data);

    if (data) {
      try {
        const { clickId, source,test } = JSON.parse(data);
        if (clickId && source === 'admesh') {
          const img = document.createElement('img');
          img.src = `https://api.useadmesh.com/conversion/pixel?utm_click_id=${clickId}&&test=${test}`;
          // img.src = `http://127.0.0.1:8000/conversion/pixel?utm_click_id=${clickId}&&test=${test}`;
          img.width = 1;
          img.height = 1;
          img.style.display = 'none';
          document.body.appendChild(img);
          localStorage.removeItem(key); // prevent duplicate fire
          console.log('AdMesh conversion tracked:', clickId);
          toast.success('AdMesh conversion tracked!');
        }
      } catch (e) {
        console.error('AdMesh pixel error:', e);
      }
    }
  }, []);

  return <ChatWindow />;
}
